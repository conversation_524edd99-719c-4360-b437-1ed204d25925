# Documentation du Système RAG Confluence

Ce répertoire contient la documentation détaillée du système RAG Confluence.

## Documents disponibles

- [Journalisation structurée avec identifiants de corrélation](journalisation_structuree.md) - Documentation détaillée sur le système de journalisation structurée
- [Circuit Breaker Pattern](circuit_breaker.md) - Documentation sur l'implémentation du pattern Circuit Breaker
- [Structure des données](structure_donnees.md) - Documentation détaillée sur la structure des données

## Guides d'utilisation

- [Guide de démarrage rapide](../README.md) - Guide principal d'installation et d'utilisation
- [Configuration avancée](configuration_avancee.md) - Options de configuration avancées
- [Développement et extension](developpement.md) - Guide pour étendre et personnaliser le système

## Références techniques

- [API Reference](api_reference.md) - Documentation de référence de l'API
- [Architecture détaillée](architecture.md) - Description détaillée de l'architecture du système
- [Modèles de données](modeles_donnees.md) - Documentation des modèles de données utilisés

## Contribution

Si vous souhaitez contribuer à cette documentation, veuillez suivre ces étapes :

1. Créez un nouveau fichier Markdown dans ce répertoire
2. Ajoutez un lien vers votre document dans ce fichier README.md
3. Assurez-vous que votre documentation suit le même style et format que les documents existants
4. Soumettez une pull request avec vos modifications

## Format des documents

Tous les documents doivent suivre ces règles de formatage :

- Utiliser Markdown pour le formatage
- Inclure un titre principal (# Titre) au début du document
- Structurer le document avec des sous-titres (## Sous-titre)
- Inclure des exemples de code quand c'est pertinent
- Utiliser des tableaux pour présenter des informations structurées
- Inclure des liens vers d'autres documents pertinents
