# Confluence RAG System

Système de Retrieval Augmented Generation (RAG) pour Confluence. Ce système permet de récupérer et traiter du contenu depuis Confluence pour l'utiliser dans des applications de génération augmentée par récupération (RAG).

## Fonctionnalités Principales

* **Authentification avec token PAT** : Utilisation du Personal Access Token (PAT) pour l'authentification à Confluence.
* **Récupération de contenu** : Pages, blogs, et leurs pages enfants depuis Confluence via l'API REST.
* **Navigation hiérarchique** : Récupération récursive des pages enfants jusqu'à une profondeur configurable.
* **Filtrage flexible** : Basé sur les espaces, les étiquettes, les types de contenu et la date de dernière modification.
* **Critères de recherche personnalisables** : Configuration via un fichier JSON local ou stocké sur Google Cloud Storage.
* **Gestion des pièces jointes** : Téléchargement et traitement des pièces jointes (PDF, DOCX, PPTX, TXT, etc.).
* **Téléchargements parallèles** : Téléchargement des pièces jointes en parallèle avec un nombre configurable de téléchargements simultanés.
* **Options de stockage multiples** : Stockage sur système de fichiers local ou Google Cloud Storage (GCS).
* **Détection des changements** : Suivi des modifications entre les synchronisations pour un traitement efficace.
* **Stratégie de mise à jour intelligente** : Traitement sélectif des contenus et pièces jointes modifiés uniquement.
* **Logging structuré** : Journalisation détaillée avec identifiants de corrélation pour le suivi et le débogage.
* **Architecture modulaire** : Conception asynchrone pour de meilleures performances.

## Structure du projet

```
── confluence_rag/         # Package principal du code source
│   ├── __init__.py
│   ├── config.py           # Classes de configuration (ConfluenceConfig, SearchCriteria, StorageConfig)
│   ├── models.py           # Modèles de données (ContentItem, AttachmentDetail)
│   ├── exceptions.py       # Exceptions personnalisées
│   ├── utils.py            # Utilitaires (ex: SecurityValidator)
│   ├── client.py           # Client API Confluence et constructeur CQL
│   ├── processing.py       # Logique de traitement (AttachmentProcessor, ContentRetriever)
│   ├── tracking.py         # Suivi des changements (ConfluenceChangeTracker)
│   ├── storage.py          # Gestion du stockage (FileSystemStorage, GCSStorage)
│   ├── orchestrator.py     # Orchestration du processus de synchronisation
│   └── main.py             # Fonction principale `amain` et configuration du logging
├── tests/                  # Tests unitaires et d'intégration
├── monitoring_app.py       # Application FastAPI pour le monitoring (optionnel)
├── criteres_recherche.json # Fichier d'exemple pour les critères de recherche
├── .env.example            # Fichier d'exemple pour les variables d'environnement
├── requirements.txt        # Dépendances Python
├── run_sync.py             # Script principal pour lancer la synchronisation
└── README.md               # Ce fichier
```

## Installation

```bash
# Créer un environnement virtuel
python -m venv .venv
source .venv/bin/activate  # Sur Linux/Mac
# ou
.venv\Scripts\activate     # Sur Windows

# Installer les dépendances
pip install -r requirements.txt

# Pour utiliser Google Cloud Storage (optionnel)
pip install google-cloud-storage
```

## Configuration

1. Copiez le fichier `.env.example` vers `.env` et remplissez les variables d'environnement nécessaires :

```bash
cp .env.example .env
```

2. Éditez le fichier `.env` pour configurer :
   - L'URL de votre instance Confluence
   - Votre token d'authentification PAT
   - Les paramètres de recherche et de stockage

3. Personnalisez les critères de recherche dans le fichier `criteres_recherche.json` :

```json
{
  "spaces": ["VOTRE_ESPACE"],
  "labels": ["documentation", "important"],
  "types": ["page", "blogpost"],
  "date_min": "2023-01-01",
  "date_max": "2023-12-31",
  "creators": ["user1", "user2"],
  "keywords": ["architecture", "microservices"],
  "exclude_labels": ["obsolete", "archived"],
  "max_results": 100,
  "include_attachments": true,
  "attachment_types": ["pdf", "docx", "xlsx", "pptx"],
  "include_children": true,
  "max_children_depth": 3
}
```

Les paramètres `include_children` et `max_children_depth` contrôlent la récupération récursive des pages enfants :
- `include_children` : Active ou désactive la récupération des pages enfants (défaut: true)
- `max_children_depth` : Définit la profondeur maximale de récupération (1 = enfants directs uniquement, 2 = enfants et petits-enfants, etc.)

4. Configuration de la stratégie de mise à jour dans le fichier `.env` :

```
# Configuration de traitement
CHUNK_SIZE=1000                # Taille des chunks de texte
OVERLAP_SIZE=200               # Chevauchement entre les chunks
MAX_PARALLEL_DOWNLOADS=5       # Nombre maximum de téléchargements simultanés
MAX_THREAD_WORKERS=5           # Nombre maximum de workers pour le traitement des fichiers

# Configuration de stockage
STORAGE_TYPE=filesystem        # filesystem ou gcs
OUTPUT_DIR=output_data_dir
INCLUDE_ATTACHMENTS=true
ATTACHMENT_EXTENSIONS_TO_CONVERT=.pdf,.docx,.txt
MAX_ATTACHMENT_SIZE_MB=50

# Configuration de suivi
SYNC_REPORT_PATH=last_sync_report.json
```

## Utilisation

### Synchronisation de base

```bash
python run_sync.py
```

### Utilisation avec Google Cloud Storage

#### Pour stocker les données sur GCS

Configurez les variables suivantes dans votre fichier `.env` :

```
STORAGE_TYPE=gcs
GCS_BUCKET_NAME=votre-bucket-gcs
GCS_BASE_PREFIX=confluence_rag
```

#### Pour charger les critères de recherche depuis GCS

Option 1 - Spécifier le bucket et le chemin séparément :
```
CRITERIA_FILE_PATH=chemin/vers/criteres_recherche.json
CRITERIA_GCS_BUCKET=votre-bucket-gcs
```

Option 2 - Utiliser une URL GCS complète :
```
CRITERIA_FILE_PATH=gs://votre-bucket-gcs/chemin/vers/criteres_recherche.json
```

### Monitoring (optionnel)

Pour lancer l'application de monitoring :
```bash
python monitoring_app.py
```

Puis accédez à `http://localhost:8000/docs` pour voir la documentation de l'API.

## Exemples d'utilisation

### Synchroniser un espace spécifique

1. Modifiez `criteres_recherche.json` pour spécifier l'espace :
```json
{
  "spaces": ["MARKETING"],
  "types": ["page", "blogpost"]
}
```

2. Lancez la synchronisation :
```bash
python run_sync.py
```

### Synchroniser uniquement les documents avec certaines étiquettes

1. Modifiez `criteres_recherche.json` :
```json
{
  "spaces": ["DOCS", "TECH"],
  "labels": ["public", "documentation"],
  "exclude_labels": ["confidential", "draft"]
}
```

2. Lancez la synchronisation :
```bash
python run_sync.py
```

### Récupérer une structure hiérarchique de pages

Pour récupérer une page et toutes ses sous-pages jusqu'à une certaine profondeur :

1. Modifiez `criteres_recherche.json` :
```json
{
  "spaces": ["DOCS"],
  "labels": ["documentation"],
  "include_children": true,
  "max_children_depth": 5
}
```

2. Lancez la synchronisation :
```bash
python run_sync.py
```

Cette configuration récupérera toutes les pages de l'espace "DOCS" avec l'étiquette "documentation", ainsi que leurs pages enfants jusqu'à 5 niveaux de profondeur.

## Architecture

Le système est conçu avec une architecture modulaire et asynchrone pour optimiser les performances lors de la récupération et du traitement des données Confluence.

### Diagramme d'architecture

```mermaid
graph TD
    %% Subgraph for User Interaction
    subgraph UserInteraction["Interaction Utilisateur/Déploiement"]
        A[run_sync.py] -->|Loads| B{".env (Variables d'Environnement)"}
        A -->|Reads| C[criteres_recherche.json]
    end

    %% Core Application Subgraph
    subgraph CoreApplication["Package: confluence_rag"]
        D[main.py] -->|Configures| E[config.py: ConfluenceConfig, SearchCriteria, StorageConfig]
        D -->|Orchestrates| F[orchestrator.py: SyncOrchestrator]
        D -->|Configures Logging to| U[confluence_rag.log]

        F -->|Tracks| G[tracking.py: ConfluenceChangeTracker]
        F -->|Processes| H[processing.py: ContentRetriever]
        F -->|Stores| V[storage.py: StorageProvider]

        H -->|Queries| I[client.py: ConfluenceClient]
        H -->|Handles| J[processing.py: AttachmentProcessor]
    end

    %% Data Storage Subgraph
    subgraph DataStorage["Stockage de Données"]
        V -->|Saves to| R["Système de fichiers local"]
        V -->|Saves to| S["Google Cloud Storage (GCS)"]
    end
```

### Flux de données

1. **Configuration** : Chargement des paramètres depuis `.env` et `criteres_recherche.json`
2. **Récupération initiale** : Interrogation de l'API Confluence selon les critères spécifiés
3. **Récupération hiérarchique** : Récupération récursive des pages enfants jusqu'à la profondeur spécifiée
4. **Traitement** : Extraction du texte des pages et des pièces jointes
5. **Suivi des changements** : Détection des contenus modifiés depuis la dernière synchronisation
6. **Stockage** : Enregistrement des données traitées sur le système de fichiers ou GCS
7. **Rapport** : Génération d'un rapport de synchronisation avec les statistiques

### Stratégie de mise à jour

Le système utilise une stratégie de mise à jour intelligente pour optimiser les performances et réduire la charge sur l'API Confluence :

1. **Détection des changements** :
   - Utilisation d'un système de hachage pour détecter les modifications dans les contenus et pièces jointes
   - Stockage des hachages dans des fichiers JSON pour comparaison lors des synchronisations ultérieures
   - Prise en compte des métadonnées (date de modification, version) et du contenu pour la détection

2. **Traitement sélectif** :
   - Seuls les contenus et pièces jointes modifiés sont traités et stockés
   - Les contenus inchangés sont ignorés pour économiser des ressources

3. **Filtrage des pièces jointes** :
   - Filtrage par extension de fichier (configurable via `attachment_extensions_to_convert`)
   - Filtrage par taille maximale (configurable via `max_attachment_size_mb`)
   - Téléchargement et traitement uniquement des pièces jointes qui passent les filtres

4. **Téléchargements parallèles** :
   - Téléchargement des pièces jointes en parallèle pour optimiser les performances
   - Limitation du nombre de téléchargements simultanés (configurable via `MAX_PARALLEL_DOWNLOADS`)
   - Traitement des fichiers en parallèle avec un pool de threads (configurable via `MAX_THREAD_WORKERS`)

5. **Stockage optimisé** :
   - Organisation hiérarchique des données (contenus et pièces jointes)
   - Nommage cohérent des fichiers pour éviter les collisions
   - Métadonnées de stockage ajoutées pour faciliter le suivi

### Structure des données

Le système organise les données de manière hiérarchique pour faciliter l'accès et la gestion :

1. **Identification des contenus** :
   - Chaque page (ContentItem) possède un ID unique (`content_id`)
   - Chaque pièce jointe (AttachmentDetail) possède également un ID unique (`attachment_id`)
   - Les pièces jointes sont associées à une page via le champ `content_id`

2. **Organisation du stockage** :
   - **Système de fichiers** :
     ```
     output_data_dir/
     ├── contents/
     │   ├── 123456.json  # Page avec ID 123456
     │   └── 789012.json  # Page avec ID 789012
     ├── attachments/
     │   ├── 123456/      # Répertoire pour les pièces jointes de la page 123456
     │   │   ├── att001_document.pdf
     │   │   └── att002_image.png
     │   └── 789012/      # Répertoire pour les pièces jointes de la page 789012
     │       ├── att003_spreadsheet.xlsx
     │       └── att004_presentation.pptx
     ```

   - **Google Cloud Storage** :
     ```
     gs://votre-bucket/confluence_rag/
     ├── contents/
     │   ├── 123456.json
     │   └── 789012.json
     └── attachments/
         ├── 123456/
         │   ├── att001_document.pdf
         │   └── att002_image.png
         └── 789012/
             ├── att003_spreadsheet.xlsx
             └── att004_presentation.pptx
     ```

3. **Accès aux données** :
   - Pour récupérer une page : utiliser son ID (`content_id`)
   - Pour récupérer toutes les pièces jointes d'une page : utiliser l'ID de la page
   - Pour récupérer une pièce jointe spécifique : utiliser l'ID de la page et l'ID de la pièce jointe

Pour une documentation plus détaillée sur la structure des données, consultez [Structure des données](docs/structure_donnees.md).

### Extensibilité

Le système est conçu pour être facilement extensible :

- **Nouveaux fournisseurs de stockage** : Implémentez l'interface `StorageProvider` pour ajouter de nouveaux backends de stockage
- **Formats de pièces jointes supplémentaires** : Étendez la classe `AttachmentProcessor` pour prendre en charge d'autres types de documents
- **Critères de recherche personnalisés** : Modifiez la méthode `to_cql()` de la classe `SearchCriteria` pour ajouter de nouveaux critères

### Journalisation structurée avec identifiants de corrélation

Le système implémente une journalisation structurée avancée avec des identifiants de corrélation pour faciliter le suivi et le débogage :

1. **Format JSON structuré** : Les logs sont formatés en JSON pour faciliter leur analyse avec des outils comme Elasticsearch, Kibana ou Splunk
2. **Identifiants de corrélation** : Chaque opération reçoit un identifiant unique qui est propagé à travers tous les composants du système
3. **Propagation automatique** : Les identifiants sont automatiquement propagés à travers les appels asynchrones
4. **Contexte enrichi** : Les logs incluent des informations contextuelles comme le module, la fonction, la ligne de code, etc.
5. **Configuration flexible** : La journalisation structurée peut être activée/désactivée via la configuration

Configuration dans le fichier `.env` :
```
# Configuration de logging
LOG_LEVEL=INFO                    # Niveau de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_FILE=confluence_rag.log       # Chemin du fichier de log
STRUCTURED_LOGGING=true           # Utiliser le format JSON structuré pour les logs
```

Exemple de log structuré :
```json
{
  "timestamp": "2023-06-15T14:32:45.123456",
  "level": "INFO",
  "logger": "confluence_rag.orchestrator",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "Début de la synchronisation avec Confluence",
  "module": "orchestrator",
  "function": "run",
  "line": 86,
  "thread_id": 123456,
  "thread_name": "MainThread",
  "process_id": 654321
}
```

Les identifiants de corrélation permettent de suivre facilement le flux d'exécution à travers les différents composants du système, même dans un environnement asynchrone complexe.

### Mécanismes de résilience

Le système intègre plusieurs mécanismes pour assurer sa résilience face aux erreurs :

1. **Retry avec backoff exponentiel** : Les appels à l'API Confluence sont automatiquement réessayés en cas d'échec temporaire, avec un délai croissant entre les tentatives pour éviter de surcharger le serveur
2. **Circuit Breaker Pattern** : Protection contre les défaillances en cascade en détectant les échecs répétés et en bloquant temporairement les appels à un service défaillant. [Documentation détaillée](docs/circuit_breaker.md)
3. **Validation des entrées** : Toutes les entrées sont validées avant traitement pour éviter les erreurs liées aux données invalides
4. **Gestion des erreurs** : Les erreurs sont capturées, journalisées et gérées de manière appropriée à chaque niveau du système
5. **Reprise après interruption** : Le système peut reprendre une synchronisation interrompue grâce au suivi des changements