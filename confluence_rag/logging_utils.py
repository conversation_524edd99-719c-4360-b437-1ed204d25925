#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitaires de journalisation structurée pour le système RAG Confluence.
"""

import json
import logging
import uuid
import threading
import functools
import asyncio
import re
from typing import Dict, Any, Optional, Callable, TypeVar, Awaitable, cast
from datetime import datetime
import traceback
import sys
from contextvars import ContextVar

# Type variables pour les décorateurs
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Awaitable[Any]])


class CorrelationContext:
    """
    Contexte pour stocker et récupérer les identifiants de corrélation.
    Utilise un contexte local au thread et un contexte de tâche asyncio.
    """
    _thread_local = threading.local()
    _task_context_var = ContextVar('correlation_id', default=None)

    @classmethod
    def get_correlation_id(cls) -> Optional[str]:
        """
        Récupère l'identifiant de corrélation actuel.
        Vérifie d'abord le contexte asyncio, puis le contexte de thread.
        """
        # Vérifier d'abord le contexte asyncio
        correlation_id = cls._task_context_var.get()
        if correlation_id:
            return correlation_id

        # Sinon, vérifier le contexte de thread
        return getattr(cls._thread_local, 'correlation_id', None)

    @classmethod
    def set_correlation_id(cls, correlation_id: str) -> None:
        """
        Définit l'identifiant de corrélation dans les deux contextes.
        """
        # Définir dans le contexte de thread
        cls._thread_local.correlation_id = correlation_id

        # Définir dans le contexte asyncio si nous sommes dans une boucle asyncio
        try:
            cls._task_context_var.set(correlation_id)
        except RuntimeError:
            # Pas dans une boucle asyncio, ignorer
            pass

    @classmethod
    def generate_correlation_id(cls) -> str:
        """
        Génère un nouvel identifiant de corrélation unique et le définit comme identifiant actuel.
        """
        correlation_id = str(uuid.uuid4())
        cls.set_correlation_id(correlation_id)
        return correlation_id

    @classmethod
    def clear_correlation_id(cls) -> None:
        """
        Efface l'identifiant de corrélation des deux contextes.
        """
        # Effacer du contexte de thread
        if hasattr(cls._thread_local, 'correlation_id'):
            delattr(cls._thread_local, 'correlation_id')

        # Essayer d'effacer du contexte asyncio
        try:
            cls._task_context_var.set(None)
        except RuntimeError:
            # Pas dans une boucle asyncio, ignorer
            pass


class SecurityFilter(logging.Filter):
    """
    Filtre de logging qui supprime les informations sensibles des messages de log.
    """

    @staticmethod
    def _sanitize_message(message: str) -> str:
        """
        Nettoie un message de log en supprimant les informations sensibles.

        Args:
            message: Le message de log original

        Returns:
            Le message de log nettoyé sans informations sensibles
        """
        # Patterns pour identifier et masquer les tokens et informations sensibles
        patterns = [
            # API Tokens classiques - format email:token (doit être avant le pattern PAT général)
            (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}:[A-Za-z0-9]{16,}', '***EMAIL:TOKEN***'),
            # Personal Access Tokens (PAT) - généralement des chaînes alphanumériques longues
            (r'[A-Za-z0-9]{20,}', '***TOKEN***'),
            # Headers Authorization
            (r'Authorization:\s*Bearer\s+[A-Za-z0-9._-]+', 'Authorization: Bearer ***TOKEN***'),
            (r'Authorization:\s*Basic\s+[A-Za-z0-9+/=]+', 'Authorization: Basic ***TOKEN***'),
            # Mots de passe dans les URLs
            (r'://[^:]+:[^@]+@', '://***:***@'),
            # Tokens dans les paramètres d'URL
            (r'([?&])(token|password|secret|key)=([^&\s]+)', r'\1\2=***'),
            # Clés d'API dans les chaînes JSON
            (r'"(token|password|secret|key|api_key|access_token|pat_token|api_token)"\s*:\s*"[^"]*"', r'"\1": "***"'),
        ]

        sanitized_message = str(message)
        for pattern, replacement in patterns:
            sanitized_message = re.sub(pattern, replacement, sanitized_message, flags=re.IGNORECASE)

        return sanitized_message

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Nettoie le message de log et ajoute l'identifiant de corrélation.
        """
        # Nettoyer le message de log
        if hasattr(record, 'msg') and record.msg:
            record.msg = self._sanitize_message(str(record.msg))

        # Nettoyer les arguments du message
        if hasattr(record, 'args') and record.args:
            sanitized_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    sanitized_args.append(self._sanitize_message(arg))
                else:
                    sanitized_args.append(arg)
            record.args = tuple(sanitized_args)

        # Nettoyer les attributs supplémentaires (pour le logging structuré)
        for key, value in record.__dict__.items():
            if not key.startswith('_') and key not in ['name', 'msg', 'args', 'levelname', 'levelno',
                                                       'pathname', 'filename', 'module', 'lineno',
                                                       'funcName', 'created', 'msecs', 'relativeCreated',
                                                       'thread', 'threadName', 'processName', 'process',
                                                       'exc_info', 'exc_text', 'stack_info']:
                if isinstance(value, str):
                    setattr(record, key, self._sanitize_message(value))
                elif isinstance(value, dict):
                    # Nettoyer les dictionnaires récursivement
                    sanitized_dict = {}
                    for k, v in value.items():
                        if isinstance(v, str):
                            sanitized_dict[k] = self._sanitize_message(v)
                        else:
                            sanitized_dict[k] = v
                    setattr(record, key, sanitized_dict)

        # Toujours autoriser l'enregistrement
        return True


class CorrelationIdFilter(logging.Filter):
    """
    Filtre de logging qui ajoute l'identifiant de corrélation actuel aux enregistrements de log.
    """

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Ajoute l'identifiant de corrélation à l'enregistrement de log.
        """
        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()

        # Ajouter l'identifiant à l'enregistrement
        record.correlation_id = correlation_id or 'no-correlation-id'

        # Toujours autoriser l'enregistrement
        return True


class StructuredLogFormatter(logging.Formatter):
    """
    Formateur de logs qui produit des logs structurés au format JSON.
    """

    def __init__(self, include_traceback: bool = True):
        """
        Initialise le formateur.

        Args:
            include_traceback: Si True, inclut la trace d'appel pour les erreurs
        """
        super().__init__()
        self.include_traceback = include_traceback

    def format(self, record: logging.LogRecord) -> str:
        """
        Formate l'enregistrement de log en JSON structuré.
        """
        # Créer la structure de base du log
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "correlation_id": getattr(record, 'correlation_id', 'no-correlation-id'),
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread_id": record.thread,
            "thread_name": record.threadName,
            "process_id": record.process
        }

        # Ajouter les informations d'exception si présentes
        if record.exc_info and self.include_traceback:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": "".join(traceback.format_exception(*record.exc_info))
            }

        # Ajouter les attributs supplémentaires
        for key, value in record.__dict__.items():
            if key not in log_data and not key.startswith('_') and key != 'exc_info':
                try:
                    json.dumps({key: value})  # Tester si sérialisable
                    log_data[key] = value
                except (TypeError, OverflowError):
                    log_data[key] = str(value)

        # Convertir en JSON
        return json.dumps(log_data, ensure_ascii=False)


def with_correlation_id(func: Callable) -> Callable:
    """
    Décorateur qui génère un nouvel identifiant de corrélation pour la fonction.
    Fonctionne avec les fonctions synchrones et asynchrones.
    """
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        # Générer un nouvel identifiant de corrélation
        correlation_id = CorrelationContext.generate_correlation_id()

        try:
            # Exécuter la fonction avec l'identifiant de corrélation
            return func(*args, **kwargs)
        finally:
            # Nettoyer l'identifiant de corrélation
            CorrelationContext.clear_correlation_id()

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Générer un nouvel identifiant de corrélation
        correlation_id = CorrelationContext.generate_correlation_id()

        try:
            # Exécuter la fonction avec l'identifiant de corrélation
            return await func(*args, **kwargs)
        finally:
            # Nettoyer l'identifiant de corrélation
            CorrelationContext.clear_correlation_id()

    # Déterminer si la fonction est asynchrone
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def propagate_correlation_id(func: F) -> F:
    """
    Décorateur qui propage l'identifiant de corrélation actuel à la fonction.
    Utilisé pour les fonctions appelées à partir d'une fonction avec un identifiant de corrélation.
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()

        if correlation_id:
            # Sauvegarder l'identifiant actuel
            CorrelationContext.set_correlation_id(correlation_id)

            try:
                # Exécuter la fonction avec l'identifiant de corrélation
                return await func(*args, **kwargs)
            finally:
                # Restaurer l'identifiant de corrélation
                CorrelationContext.set_correlation_id(correlation_id)
        else:
            # Pas d'identifiant de corrélation, exécuter normalement
            return await func(*args, **kwargs)

    return cast(F, wrapper)
