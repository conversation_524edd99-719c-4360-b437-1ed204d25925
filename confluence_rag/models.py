#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Modèles de données pour le système RAG Confluence.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class UserInfo(BaseModel):
    """Informations sur un utilisateur Confluence."""
    id: str
    username: str
    display_name: str
    email: Optional[str] = None
    picture_url: Optional[str] = None


class LabelInfo(BaseModel):
    """Informations sur un label Confluence."""
    id: str
    name: str
    prefix: Optional[str] = None


class SpaceInfo(BaseModel):
    """Informations sur un espace Confluence."""
    id: str
    key: str
    name: str
    type: str
    description: Optional[str] = None
    homepage_id: Optional[str] = None


class AttachmentDetail(BaseModel):
    """Détails d'une pièce jointe Confluence."""
    id: str
    title: str
    file_name: str
    file_size: int
    media_type: str
    created: datetime
    creator: UserInfo
    download_url: str
    content_id: str
    content_type: Optional[str] = None
    extracted_text: Optional[str] = None
    processing_status: str = "pending"  # pending, processing, completed, failed
    processing_error: Optional[str] = None

    @validator('media_type')
    def validate_media_type(cls, v):
        """Valide que le type de média est supporté."""
        supported_types = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/html',
            'text/markdown'
        ]
        if v not in supported_types and not v.startswith('image/'):
            return v + " (non supporté)"
        return v


class ContentItem(BaseModel):
    """Élément de contenu Confluence (page, blog, etc.)."""
    id: str
    type: str  # page, blogpost, etc.
    status: str  # current, draft, etc.
    title: str
    space: SpaceInfo
    version: Dict[str, Any]
    created: datetime
    creator: UserInfo
    last_updated: datetime
    last_updater: UserInfo
    content_url: str
    web_ui_url: str
    body_storage: Optional[str] = None  # Format de stockage (HTML)
    body_view: Optional[str] = None  # Format d'affichage (HTML)
    body_plain: Optional[str] = None  # Texte brut
    labels: List[LabelInfo] = Field(default_factory=list)
    attachments: List[AttachmentDetail] = Field(default_factory=list)
    parent_id: Optional[str] = None
    ancestors: List[Dict[str, Any]] = Field(default_factory=list)
    children: List[Dict[str, Any]] = Field(default_factory=list)
    processed_chunks: List[Dict[str, Any]] = Field(default_factory=list)

    def get_content_summary(self, max_length: int = 200) -> str:
        """Retourne un résumé du contenu."""
        if self.body_plain:
            if len(self.body_plain) <= max_length:
                return self.body_plain
            return self.body_plain[:max_length] + "..."
        return "Contenu non disponible"