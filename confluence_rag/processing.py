#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Logique de traitement pour le système RAG Confluence.
"""

import os
import io
import logging
import tempfile
from typing import List, Dict, Any, Optional, Tuple, BinaryIO
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Bibliothèques de traitement de documents
import PyPDF2
from docx import Document
import openpyxl
from bs4 import BeautifulSoup

from .models import ContentItem, AttachmentDetail
from .client import ConfluenceClient
from .config import SearchCriteria, ProcessingConfig
from .utils import SecurityValidator, TextProcessor
from .exceptions import AttachmentProcessingError, ContentProcessingError


class AttachmentProcessor:
    """Processeur pour les pièces jointes Confluence."""

    def __init__(self, client: ConfluenceClient):
        """Initialise le processeur avec le client Confluence."""
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.executor = ThreadPoolExecutor(max_workers=5)

    async def process_attachment(self, attachment: AttachmentDetail) -> AttachmentDetail:
        """Traite une pièce jointe pour en extraire le texte."""
        try:
            # Vérifier la sécurité de la pièce jointe
            SecurityValidator.validate_attachment(attachment.file_name, attachment.media_type)

            # Mettre à jour le statut
            attachment.processing_status = "processing"

            # Télécharger la pièce jointe
            content_bytes = await self.client.download_attachment(attachment)

            # Extraire le texte selon le type de média
            extracted_text = await self._extract_text_from_bytes(
                content_bytes,
                attachment.file_name,
                attachment.media_type
            )

            # Mettre à jour l'objet AttachmentDetail
            attachment.extracted_text = extracted_text
            attachment.processing_status = "completed"

            return attachment
        except Exception as e:
            self.logger.error(f"Erreur lors du traitement de la pièce jointe {attachment.id}: {e}")
            attachment.processing_status = "failed"
            attachment.processing_error = str(e)

            if isinstance(e, AttachmentProcessingError):
                raise e
            else:
                raise AttachmentProcessingError(
                    f"Erreur lors du traitement de la pièce jointe: {e}",
                    attachment_id=attachment.id,
                    file_name=attachment.file_name
                )

    async def _extract_text_from_bytes(
        self, content_bytes: bytes, file_name: str, media_type: str
    ) -> str:
        """Extrait le texte à partir des octets de contenu selon le type de média."""
        # Créer un objet file-like à partir des octets
        file_obj = io.BytesIO(content_bytes)

        # Déterminer l'extension du fichier
        _, ext = os.path.splitext(file_name.lower())

        # Traiter selon le type de média ou l'extension
        if media_type == 'application/pdf' or ext == '.pdf':
            return await self._extract_text_from_pdf(file_obj)
        elif media_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' or ext == '.docx':
            return await self._extract_text_from_docx(file_obj)
        elif media_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or ext == '.xlsx':
            return await self._extract_text_from_xlsx(file_obj)
        elif media_type == 'text/plain' or ext in ['.txt', '.md', '.csv']:
            return content_bytes.decode('utf-8', errors='replace')
        elif media_type == 'text/html' or ext in ['.html', '.htm']:
            return self._extract_text_from_html(content_bytes.decode('utf-8', errors='replace'))
        elif media_type.startswith('image/'):
            # Pour les images, on pourrait implémenter de l'OCR ici
            return f"[Image: {file_name}]"
        else:
            return f"[Document non supporté: {file_name} ({media_type})]"

    async def _extract_text_from_pdf(self, file_obj: BinaryIO) -> str:
        """Extrait le texte d'un fichier PDF."""
        try:
            # Utiliser ThreadPoolExecutor pour exécuter le code bloquant dans un thread séparé
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, self._pdf_extraction_task, file_obj)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du PDF: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du PDF: {e}")

    def _pdf_extraction_task(self, file_obj: BinaryIO) -> str:
        """Tâche d'extraction de texte PDF à exécuter dans un thread séparé."""
        try:
            pdf_reader = PyPDF2.PdfReader(file_obj)
            text = ""

            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n\n"

            return text.strip()
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du PDF: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du PDF: {e}")

    async def _extract_text_from_docx(self, file_obj: BinaryIO) -> str:
        """Extrait le texte d'un fichier DOCX."""
        try:
            # Utiliser ThreadPoolExecutor pour exécuter le code bloquant dans un thread séparé
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, self._docx_extraction_task, file_obj)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du DOCX: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du DOCX: {e}")

    def _docx_extraction_task(self, file_obj: BinaryIO) -> str:
        """Tâche d'extraction de texte DOCX à exécuter dans un thread séparé."""
        try:
            doc = Document(file_obj)
            text = ""

            for para in doc.paragraphs:
                text += para.text + "\n"

            # Extraire le texte des tableaux
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " | "
                    text += "\n"
                text += "\n"

            return text.strip()
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du DOCX: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du DOCX: {e}")

    async def _extract_text_from_xlsx(self, file_obj: BinaryIO) -> str:
        """Extrait le texte d'un fichier XLSX."""
        try:
            # Utiliser ThreadPoolExecutor pour exécuter le code bloquant dans un thread séparé
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, self._xlsx_extraction_task, file_obj)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du XLSX: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du XLSX: {e}")

    def _xlsx_extraction_task(self, file_obj: BinaryIO) -> str:
        """Tâche d'extraction de texte XLSX à exécuter dans un thread séparé."""
        try:
            workbook = openpyxl.load_workbook(file_obj, read_only=True, data_only=True)
            text = ""

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"Feuille: {sheet_name}\n"

                for row in sheet.iter_rows(values_only=True):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    if row_text.strip():
                        text += row_text + "\n"

                text += "\n"

            return text.strip()
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du XLSX: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du XLSX: {e}")

    def _extract_text_from_html(self, html_content: str) -> str:
        """Extrait le texte d'un contenu HTML."""
        try:
            return TextProcessor.html_to_plain_text(html_content)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction du texte du HTML: {e}")
            raise AttachmentProcessingError(f"Erreur lors de l'extraction du texte du HTML: {e}")


class ContentRetriever:
    """Récupérateur de contenu Confluence."""

    def __init__(self, client: ConfluenceClient):
        """Initialise le récupérateur avec le client Confluence."""
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.attachment_processor = AttachmentProcessor(client)

    async def retrieve_content(self, content_id: str, process_attachments: bool = True) -> ContentItem:
        """Récupère un contenu Confluence avec ses pièces jointes."""
        try:
            # Récupérer le contenu
            content_item = await self.client.get_content(content_id)

            # Extraire le texte brut du contenu HTML
            if content_item.body_view:
                content_item.body_plain = TextProcessor.html_to_plain_text(content_item.body_view)

            # Récupérer et traiter les pièces jointes si demandé
            if process_attachments:
                attachments = await self.client.get_attachments(content_id)

                # Traiter les pièces jointes en parallèle
                processed_attachments = await asyncio.gather(
                    *[self.attachment_processor.process_attachment(attachment) for attachment in attachments],
                    return_exceptions=True
                )

                # Filtrer les exceptions
                content_item.attachments = [
                    attachment for attachment in processed_attachments
                    if not isinstance(attachment, Exception)
                ]

            # Découper le contenu en morceaux
            if content_item.body_plain:
                chunk_size = int(os.getenv("CHUNK_SIZE", "1000"))
                overlap_size = int(os.getenv("OVERLAP_SIZE", "200"))

                chunks = TextProcessor.chunk_text(
                    content_item.body_plain,
                    chunk_size=chunk_size,
                    overlap=overlap_size
                )

                content_item.processed_chunks = [
                    {
                        "chunk_id": f"{content_id}_chunk_{i}",
                        "content": chunk,
                        "start_index": i * (chunk_size - overlap_size) if i > 0 else 0,
                        "metadata": {
                            "title": content_item.title,
                            "space_key": content_item.space.key,
                            "content_type": content_item.type,
                            "url": content_item.web_ui_url
                        }
                    }
                    for i, chunk in enumerate(chunks)
                ]

            return content_item
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du contenu {content_id}: {e}")

            if isinstance(e, ContentProcessingError):
                raise e
            else:
                raise ContentProcessingError(
                    f"Erreur lors de la récupération du contenu: {e}",
                    content_id=content_id
                )

    async def search_and_retrieve(
        self, criteria: SearchCriteria, process_attachments: bool = True
    ) -> List[ContentItem]:
        """Recherche et récupère des contenus selon les critères spécifiés."""
        try:
            # Rechercher les contenus
            content_items = await self.client.search_content(criteria)

            # Récupérer les détails complets pour chaque contenu
            detailed_items = []
            for item in content_items:
                try:
                    detailed_item = await self.retrieve_content(item.id, process_attachments)
                    detailed_items.append(detailed_item)

                    # Récupérer récursivement les pages enfants si demandé
                    if criteria.include_children and detailed_item.children:
                        self.logger.info(
                            f"La page {detailed_item.id} - {detailed_item.title} a {len(detailed_item.children)} "
                            f"pages enfants. Récupération récursive activée (profondeur max: {criteria.max_children_depth})"
                        )
                        child_items = await self._retrieve_children(
                            detailed_item,
                            criteria,
                            process_attachments,
                            current_depth=1
                        )
                        self.logger.info(
                            f"Récupération récursive terminée pour {detailed_item.id} - {detailed_item.title}: "
                            f"{len(child_items)} pages enfants récupérées au total"
                        )
                        detailed_items.extend(child_items)
                except Exception as e:
                    self.logger.error(f"Erreur lors de la récupération détaillée du contenu {item.id}: {e}")
                    # Continuer avec le contenu suivant

            return detailed_items
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche et récupération de contenu: {e}")
            raise

    async def _retrieve_children(
        self, parent: ContentItem, criteria: SearchCriteria,
        process_attachments: bool, current_depth: int
    ) -> List[ContentItem]:
        """Récupère récursivement les pages enfants jusqu'à la profondeur spécifiée."""
        if current_depth > criteria.max_children_depth:
            return []

        self.logger.info(
            f"Récupération des pages enfants de {parent.id} - {parent.title} "
            f"(profondeur {current_depth}/{criteria.max_children_depth})"
        )

        child_items = []
        child_count = len(parent.children)
        self.logger.info(f"Nombre de pages enfants à récupérer: {child_count}")

        for i, child_info in enumerate(parent.children):
            try:
                # Récupérer l'ID de la page enfant
                child_id = child_info.get('id')
                if not child_id:
                    self.logger.warning(f"ID manquant pour une page enfant de {parent.id}")
                    continue

                child_title = child_info.get('title', 'Sans titre')
                self.logger.info(f"Récupération de la page enfant {i+1}/{child_count}: {child_id} - {child_title}")

                # Récupérer les détails de la page enfant
                child_item = await self.retrieve_content(child_id, process_attachments)
                child_items.append(child_item)

                # Récupérer récursivement les enfants de cette page
                if child_item.children and current_depth < criteria.max_children_depth:
                    self.logger.info(
                        f"La page {child_id} - {child_item.title} a {len(child_item.children)} sous-pages, "
                        f"récupération récursive (profondeur {current_depth + 1})"
                    )
                    grandchildren = await self._retrieve_children(
                        child_item,
                        criteria,
                        process_attachments,
                        current_depth + 1
                    )
                    child_items.extend(grandchildren)
                    self.logger.info(f"{len(grandchildren)} sous-pages récupérées pour {child_item.title}")
            except Exception as e:
                self.logger.error(
                    f"Erreur lors de la récupération de la page enfant {child_info.get('id', 'unknown')}: {e}"
                )
                # Continuer avec l'enfant suivant

        self.logger.info(
            f"Récupération terminée pour les pages enfants de {parent.id} - {parent.title}: "
            f"{len(child_items)} pages récupérées au total"
        )

        return child_items