#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Client API Confluence et constructeur de requêtes CQL.
"""

import logging
import time
import aiohttp
import asyncio
import functools
import re
from typing import List, Dict, Any, Optional, Union
from atlassian import Confluence

from .config import ConfluenceConfig, SearchCriteria
from .models import ContentItem, AttachmentDetail, UserInfo, SpaceInfo, LabelInfo
from .utils import <PERSON>try<PERSON>and<PERSON>
from .circuit_breaker import CircuitBreaker
from .exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError, CircuitOpenError
)


class ConfluenceClient:
    """Client pour l'API Confluence."""

    @staticmethod
    def _sanitize_error_message(error_message: str) -> str:
        """
        Nettoie un message d'erreur en supprimant les informations sensibles.

        Args:
            error_message: Le message d'erreur original

        Returns:
            Le message d'erreur nettoyé sans informations sensibles
        """
        # Patterns pour identifier et masquer les tokens et informations sensibles
        patterns = [
            # API Tokens classiques - format email:token (doit être avant le pattern PAT général)
            (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}:[A-Za-z0-9]{16,}', '***EMAIL:TOKEN***'),
            # Personal Access Tokens (PAT) - généralement des chaînes alphanumériques longues
            (r'[A-Za-z0-9]{20,}', '***TOKEN***'),
            # Headers Authorization
            (r'Authorization:\s*Bearer\s+[A-Za-z0-9._-]+', 'Authorization: Bearer ***TOKEN***'),
            (r'Authorization:\s*Basic\s+[A-Za-z0-9+/=]+', 'Authorization: Basic ***TOKEN***'),
            # Mots de passe dans les URLs
            (r'://[^:]+:[^@]+@', '://***:***@'),
            # Tokens dans les paramètres d'URL
            (r'([?&])(token|password|secret|key)=([^&\s]+)', r'\1\2=***'),
        ]

        sanitized_message = str(error_message)
        for pattern, replacement in patterns:
            sanitized_message = re.sub(pattern, replacement, sanitized_message, flags=re.IGNORECASE)

        return sanitized_message

    def __init__(self, config: ConfluenceConfig):
        """Initialise le client avec la configuration fournie."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialiser le Circuit Breaker
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_config)
        self.retry_config = config.retry_config

        # Initialiser le client Confluence en fonction du type de token fourni
        if config.pat_token:
            # Utiliser le Personal Access Token (PAT)
            self.logger.info("Utilisation de l'authentification par Personal Access Token (PAT)")
            self.client = Confluence(
                url=config.url,
                token=config.pat_token,
                timeout=config.timeout
            )
        elif config.api_token and config.username:
            # Utiliser l'API token classique (méthode obsolète mais toujours supportée)
            self.logger.info("Utilisation de l'authentification par API token classique")
            self.client = Confluence(
                url=config.url,
                username=config.username,
                password=config.api_token,
                timeout=config.timeout
            )
        else:
            raise AuthenticationError("Configuration d'authentification invalide: fournissez soit un PAT token, soit un username et API token")

        self.logger.info(f"Configuration de retry: max_retries={self.retry_config.max_retries}, "
                         f"initial_backoff={self.retry_config.initial_backoff}s, "
                         f"max_backoff={self.retry_config.max_backoff}s")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_get_content",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def get_content(self, content_id: str) -> ContentItem:
        """Récupère un contenu par son ID."""
        try:
            # Utiliser asyncio.to_thread pour rendre l'appel asynchrone
            # car la bibliothèque atlassian-python-api est synchrone
            content = await asyncio.to_thread(
                self.client.get_page_by_id,
                page_id=content_id,
                expand='body.storage,body.view,version,space,ancestors,children.page,metadata.labels'
            )

            if not content:
                raise ContentNotFoundError(f"Contenu non trouvé avec l'ID: {content_id}")

            return self._parse_content_response(content)
        except Exception as e:
            if "401" in str(e):
                raise AuthenticationError("Erreur d'authentification à l'API Confluence")
            elif "404" in str(e):
                raise ContentNotFoundError(f"Contenu non trouvé avec l'ID: {content_id}")
            elif "429" in str(e):
                retry_after = 60  # Valeur par défaut
                raise RateLimitExceededError(
                    "Limite de taux d'appels API dépassée",
                    retry_after=retry_after
                )
            else:
                sanitized_error = self._sanitize_error_message(str(e))
                self.logger.error(f"Erreur lors de la récupération du contenu {content_id}: {sanitized_error}")
                raise APIError(f"Erreur lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_search_content",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche des contenus selon les critères spécifiés."""
        try:
            cql = criteria.to_cql()
            self.logger.debug(f"Requête CQL: {cql}")

            results = []
            start = 0
            limit = min(criteria.max_results, 100)  # Limite par page

            while True:
                # Utiliser asyncio.to_thread pour rendre l'appel asynchrone
                response = await asyncio.to_thread(
                    self.client.cql,
                    cql=cql,
                    start=start,
                    limit=limit,
                    expand='body.storage,body.view,version,space,ancestors,children.page,metadata.labels'
                )

                if not response or 'results' not in response:
                    break

                for result in response['results']:
                    content_item = self._parse_content_response(result)
                    results.append(content_item)

                # Vérifier s'il y a plus de résultats
                if len(response['results']) < limit or len(results) >= criteria.max_results:
                    break

                start += limit

            return results[:criteria.max_results]
        except Exception as e:
            if "401" in str(e):
                raise AuthenticationError("Erreur d'authentification à l'API Confluence")
            elif "429" in str(e):
                retry_after = 60  # Valeur par défaut
                raise RateLimitExceededError(
                    "Limite de taux d'appels API dépassée",
                    retry_after=retry_after
                )
            else:
                sanitized_error = self._sanitize_error_message(str(e))
                self.logger.error(f"Erreur lors de la recherche de contenu: {sanitized_error}")
                raise APIError(f"Erreur lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_get_attachments",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def get_attachments(self, content_id: str) -> List[AttachmentDetail]:
        """Récupère les pièces jointes d'un contenu."""
        try:
            # Utiliser asyncio.to_thread pour rendre l'appel asynchrone
            attachments = await asyncio.to_thread(
                self.client.get_attachments_from_content,
                content_id
            )

            if not attachments or 'results' not in attachments:
                return []

            attachment_details = []
            for attachment in attachments['results']:
                attachment_detail = self._parse_attachment_response(attachment, content_id)
                attachment_details.append(attachment_detail)

            return attachment_details
        except Exception as e:
            if "401" in str(e):
                raise AuthenticationError("Erreur d'authentification à l'API Confluence")
            elif "404" in str(e):
                # Retourner une liste vide si le contenu n'existe pas
                self.logger.warning(f"Contenu non trouvé lors de la récupération des pièces jointes: {content_id}")
                return []
            elif "429" in str(e):
                retry_after = 60  # Valeur par défaut
                raise RateLimitExceededError(
                    "Limite de taux d'appels API dépassée",
                    retry_after=retry_after
                )
            else:
                sanitized_error = self._sanitize_error_message(str(e))
                self.logger.error(f"Erreur lors de la récupération des pièces jointes pour {content_id}: {sanitized_error}")
                raise APIError(f"Erreur lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError, aiohttp.ClientError, asyncio.TimeoutError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_download_attachment",
        exceptions_to_trip=(APIError, RateLimitExceededError, aiohttp.ClientError)
    )
    async def download_attachment(self, attachment: AttachmentDetail) -> bytes:
        """Télécharge une pièce jointe."""
        try:
            # L'API Atlassian-Python ne fournit pas de méthode asynchrone pour le téléchargement
            # Nous utilisons donc aiohttp directement
            async with aiohttp.ClientSession() as session:
                # Configurer l'authentification en fonction du type de token
                if self.config.pat_token:
                    # Utiliser le Personal Access Token (PAT)
                    headers = {"Authorization": f"Bearer {self.config.pat_token}"}
                    # Ajouter un timeout pour éviter les requêtes bloquées indéfiniment
                    timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                    async with session.get(attachment.download_url, headers=headers, timeout=timeout) as response:
                        if response.status == 200:
                            return await response.read()
                        elif response.status == 401:
                            raise AuthenticationError("Erreur d'authentification lors du téléchargement")
                        elif response.status == 404:
                            raise ContentNotFoundError(f"Pièce jointe non trouvée: {attachment.id}")
                        elif response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', 60))
                            raise RateLimitExceededError(
                                "Limite de taux d'appels API dépassée lors du téléchargement",
                                retry_after=retry_after
                            )
                        else:
                            raise APIError(
                                f"Erreur lors du téléchargement de la pièce jointe: {response.status}",
                                status_code=response.status
                            )
                elif self.config.api_token and self.config.username:
                    # Utiliser l'authentification basique avec API token
                    auth = aiohttp.BasicAuth(self.config.username, self.config.api_token)
                    # Ajouter un timeout pour éviter les requêtes bloquées indéfiniment
                    timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                    async with session.get(attachment.download_url, auth=auth, timeout=timeout) as response:
                        if response.status == 200:
                            return await response.read()
                        elif response.status == 401:
                            raise AuthenticationError("Erreur d'authentification lors du téléchargement")
                        elif response.status == 404:
                            raise ContentNotFoundError(f"Pièce jointe non trouvée: {attachment.id}")
                        elif response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', 60))
                            raise RateLimitExceededError(
                                "Limite de taux d'appels API dépassée lors du téléchargement",
                                retry_after=retry_after
                            )
                        else:
                            raise APIError(
                                f"Erreur lors du téléchargement de la pièce jointe: {response.status}",
                                status_code=response.status
                            )
                else:
                    raise AuthenticationError("Configuration d'authentification invalide pour le téléchargement")
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            sanitized_error = self._sanitize_error_message(str(e))
            self.logger.error(f"Erreur réseau lors du téléchargement de {attachment.id}: {sanitized_error}")
            raise APIError(f"Erreur réseau lors du téléchargement: {sanitized_error}")

    def _parse_content_response(self, response: Dict[str, Any]) -> ContentItem:
        """Parse la réponse de l'API pour créer un objet ContentItem."""
        # Extraire les informations de base
        content_id = response.get('id')
        content_type = response.get('type', 'unknown')
        status = response.get('status', 'unknown')
        title = response.get('title', 'Sans titre')

        # Extraire les informations de version
        version = response.get('version', {})

        # Extraire les informations d'espace
        space_data = response.get('space', {})
        space = SpaceInfo(
            id=space_data.get('id', ''),
            key=space_data.get('key', ''),
            name=space_data.get('name', ''),
            type=space_data.get('type', 'global'),
            description=space_data.get('description', {}).get('plain', {}).get('value', None),
            homepage_id=space_data.get('homepage', {}).get('id', None)
        )

        # Extraire les informations de créateur et de dernier modificateur
        creator_data = response.get('history', {}).get('createdBy', {})
        creator = UserInfo(
            id=creator_data.get('accountId', ''),
            username=creator_data.get('username', ''),
            display_name=creator_data.get('displayName', ''),
            email=creator_data.get('email', None),
            picture_url=creator_data.get('profilePicture', {}).get('path', None)
        )

        last_updater_data = response.get('history', {}).get('lastUpdated', {}).get('by', {})
        last_updater = UserInfo(
            id=last_updater_data.get('accountId', ''),
            username=last_updater_data.get('username', ''),
            display_name=last_updater_data.get('displayName', ''),
            email=last_updater_data.get('email', None),
            picture_url=last_updater_data.get('profilePicture', {}).get('path', None)
        )

        # Extraire les dates de création et de dernière mise à jour
        created = response.get('history', {}).get('createdDate', None)
        last_updated = response.get('history', {}).get('lastUpdated', {}).get('when', None)

        # Extraire les URLs
        content_url = f"{self.config.url}/rest/api/content/{content_id}"
        web_ui_url = f"{self.config.url}/pages/viewpage.action?pageId={content_id}"

        # Extraire le contenu du corps
        body_storage = response.get('body', {}).get('storage', {}).get('value', None)
        body_view = response.get('body', {}).get('view', {}).get('value', None)

        # Extraire les labels
        labels_data = response.get('metadata', {}).get('labels', {}).get('results', [])
        labels = []
        for label_data in labels_data:
            label = LabelInfo(
                id=label_data.get('id', ''),
                name=label_data.get('name', ''),
                prefix=label_data.get('prefix', None)
            )
            labels.append(label)

        # Extraire les informations sur les ancêtres et les enfants
        ancestors = response.get('ancestors', [])
        children = response.get('children', {}).get('page', {}).get('results', [])
        parent_id = ancestors[-1]['id'] if ancestors else None

        # Créer l'objet ContentItem
        content_item = ContentItem(
            id=content_id,
            type=content_type,
            status=status,
            title=title,
            space=space,
            version=version,
            created=created,
            creator=creator,
            last_updated=last_updated,
            last_updater=last_updater,
            content_url=content_url,
            web_ui_url=web_ui_url,
            body_storage=body_storage,
            body_view=body_view,
            labels=labels,
            parent_id=parent_id,
            ancestors=ancestors,
            children=children
        )

        return content_item

    def _parse_attachment_response(self, response: Dict[str, Any], content_id: str) -> AttachmentDetail:
        """Parse la réponse de l'API pour créer un objet AttachmentDetail."""
        # Extraire les informations de base
        attachment_id = response.get('id', '')
        title = response.get('title', 'Sans titre')
        file_name = response.get('title', 'unknown.file')
        file_size = response.get('extensions', {}).get('fileSize', 0)
        media_type = response.get('metadata', {}).get('mediaType', 'application/octet-stream')

        # Extraire les informations de créateur
        creator_data = response.get('extensions', {}).get('lastModifier', {})
        creator = UserInfo(
            id=creator_data.get('accountId', ''),
            username=creator_data.get('username', ''),
            display_name=creator_data.get('displayName', ''),
            email=creator_data.get('email', None),
            picture_url=creator_data.get('profilePicture', {}).get('path', None)
        )

        # Extraire la date de création
        created = response.get('extensions', {}).get('lastModified', None)

        # Construire l'URL de téléchargement
        download_url = f"{self.config.url}/download/attachments/{content_id}/{attachment_id}"

        # Créer l'objet AttachmentDetail
        attachment_detail = AttachmentDetail(
            id=attachment_id,
            title=title,
            file_name=file_name,
            file_size=file_size,
            media_type=media_type,
            created=created,
            creator=creator,
            download_url=download_url,
            content_id=content_id
        )

        return attachment_detail