#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module principal pour le système RAG Confluence.
"""

import os
import sys
import logging
import asyncio
import json
from logging.handlers import RotatingFileHandler
from typing import Optional, Dict, Any, Union

from .config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from .orchestrator import SyncOrchestrator
from .logging_utils import CorrelationIdFilter, StructuredLogFormatter


def setup_logging(log_level: str = None, log_file: str = None, structured: bool = None) -> None:
    """
    Configure le système de logging.

    Args:
        log_level: Niveau de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Chemin du fichier de log
        structured: Si True, utilise le format JSON structuré pour les logs
    """
    # Déterminer le niveau de log
    if log_level is None:
        log_level = os.getenv("LOG_LEVEL", "INFO")

    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO

    # Déterminer le fichier de log
    if log_file is None:
        log_file = os.getenv("LOG_FILE", "confluence_rag.log")

    # Déterminer si on utilise le format structuré
    if structured is None:
        structured = os.getenv("STRUCTURED_LOGGING", "true").lower() == "true"

    # Créer le filtre pour les identifiants de corrélation
    correlation_filter = CorrelationIdFilter()

    # Configurer les handlers selon le format choisi
    if structured:
        # Format JSON structuré
        formatter = StructuredLogFormatter(include_traceback=True)
    else:
        # Format texte classique avec identifiant de corrélation
        log_format = "%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"
        formatter = logging.Formatter(log_format, date_format)

    # Configurer le handler de console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(correlation_filter)

    # Configurer le handler de fichier
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.addFilter(correlation_filter)

    # Configurer le logger racine
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Supprimer les handlers existants pour éviter les doublons
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # Configurer les loggers spécifiques
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("atlassian").setLevel(logging.WARNING)

    logging.info(f"Logging configuré au niveau {log_level} avec format {'structuré' if structured else 'standard'}")


async def amain(
    config: ConfluenceConfig,
    criteria: SearchCriteria,
    storage_config: StorageConfig = None,
    processing_config: ProcessingConfig = None
) -> None:
    """Fonction principale asynchrone."""
    # Configurer le logging
    setup_logging()

    # Utiliser les configurations par défaut si non spécifiées
    if storage_config is None:
        storage_config = StorageConfig.from_env()

    if processing_config is None:
        processing_config = ProcessingConfig.from_env()

    # Créer et exécuter l'orchestrateur
    orchestrator = SyncOrchestrator(
        config,
        criteria,
        storage_config,
        processing_config
    )
    await orchestrator.run()


if __name__ == "__main__":
    # Ce code ne sera pas exécuté lorsque le module est importé
    # Il est destiné à être utilisé pour des tests rapides
    from dotenv import load_dotenv

    # Charger les variables d'environnement
    load_dotenv()

    # Créer une configuration de test
    config_params = {
        "url": os.getenv("CONFLUENCE_URL"),
        "default_space_key": os.getenv("DEFAULT_SPACE_KEY", "EXAMPLE")
    }

    # Ajouter les paramètres d'authentification selon la méthode disponible
    if os.getenv("CONFLUENCE_PAT_TOKEN"):
        # Méthode 1: Personal Access Token (PAT)
        config_params["pat_token"] = os.getenv("CONFLUENCE_PAT_TOKEN")
    elif os.getenv("CONFLUENCE_USERNAME") and os.getenv("CONFLUENCE_API_TOKEN"):
        # Méthode 2: API Token classique
        config_params["username"] = os.getenv("CONFLUENCE_USERNAME")
        config_params["api_token"] = os.getenv("CONFLUENCE_API_TOKEN")

    test_config = ConfluenceConfig(**config_params)

    # Créer des critères de recherche de test
    # Essayer de charger depuis le fichier (local ou GCS), sinon utiliser des valeurs par défaut
    criteria_file = os.getenv("CRITERIA_FILE_PATH", "criteres_recherche.json")
    gcs_bucket = os.getenv("CRITERIA_GCS_BUCKET")

    # Vérifier si le chemin du fichier est une URL GCS (gs://bucket/path)
    if criteria_file.startswith("gs://") and not gcs_bucket:
        # Extraire le bucket et le chemin de l'URL GCS
        gcs_path = criteria_file[5:]  # Supprimer le préfixe "gs://"
        bucket_end = gcs_path.find("/")
        if bucket_end > 0:
            gcs_bucket = gcs_path[:bucket_end]
            criteria_file = gcs_path[bucket_end+1:]

    try:
        test_criteria = SearchCriteria.from_file(criteria_file, gcs_bucket)
        if not test_criteria.spaces:
            test_criteria.spaces = [test_config.default_space_key]
    except Exception as e:
        print(f"Erreur lors du chargement des critères: {e}")
        test_criteria = SearchCriteria(
            spaces=[test_config.default_space_key],
            max_results=10
        )

    # Créer les configurations de test
    test_storage_config = StorageConfig.from_env()
    test_processing_config = ProcessingConfig.from_env()

    # Exécuter la fonction principale
    asyncio.run(amain(
        test_config,
        test_criteria,
        test_storage_config,
        test_processing_config
    ))