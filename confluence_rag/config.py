#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Classes de configuration pour le système RAG Confluence.
"""

import os
import json
import logging
import tempfile
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator, root_validator

# Importation conditionnelle pour GCS
try:
    from google.cloud import storage
    from google.cloud.exceptions import GoogleCloudError
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False


class RetryConfig(BaseModel):
    """Configuration pour les mécanismes de retry."""
    max_retries: int = 3  # Nombre maximum de tentatives
    initial_backoff: float = 1.0  # D<PERSON>lai initial en secondes
    max_backoff: float = 60.0  # Délai maximum en secondes
    backoff_factor: float = 2.0  # Facteur de multiplication pour le backoff exponentiel
    jitter: bool = True  # Ajouter un facteur aléatoire pour éviter les tempêtes de requêtes
    retry_on_status_codes: List[int] = Field(default_factory=lambda: [429, 500, 502, 503, 504])  # Codes HTTP à réessayer

    @classmethod
    def from_env(cls):
        """Crée une configuration de retry à partir des variables d'environnement."""
        return cls(
            max_retries=int(os.getenv("RETRY_MAX_ATTEMPTS", "3")),
            initial_backoff=float(os.getenv("RETRY_INITIAL_BACKOFF", "1.0")),
            max_backoff=float(os.getenv("RETRY_MAX_BACKOFF", "60.0")),
            backoff_factor=float(os.getenv("RETRY_BACKOFF_FACTOR", "2.0")),
            jitter=os.getenv("RETRY_JITTER", "true").lower() == "true",
            retry_on_status_codes=[int(code) for code in os.getenv("RETRY_STATUS_CODES", "429,500,502,503,504").split(",")]
        )


class ConfluenceConfig(BaseModel):
    """Configuration pour la connexion à Confluence."""
    url: str
    username: Optional[str] = None  # Optionnel avec PAT
    api_token: Optional[str] = None  # Token API classique (obsolète)
    pat_token: Optional[str] = None  # Personal Access Token (PAT)
    default_space_key: str = "EXAMPLE"
    timeout: int = 30
    retry_config: RetryConfig = Field(default_factory=RetryConfig)

    @validator('pat_token', 'api_token')
    def validate_token(cls, v, values):
        """Valide qu'au moins un type de token est fourni."""
        if not v and 'pat_token' not in values and 'api_token' not in values:
            raise ValueError("Vous devez fournir soit un PAT token, soit un API token")
        return v

    @classmethod
    def from_env(cls):
        """Crée une configuration Confluence à partir des variables d'environnement."""
        config_params = {
            "url": os.getenv("CONFLUENCE_URL", ""),
            "default_space_key": os.getenv("DEFAULT_SPACE_KEY", "EXAMPLE"),
            "timeout": int(os.getenv("CONFLUENCE_TIMEOUT", "30")),
            "retry_config": RetryConfig.from_env()
        }

        # Configurer l'authentification
        if os.getenv("CONFLUENCE_PAT_TOKEN"):
            config_params["pat_token"] = os.getenv("CONFLUENCE_PAT_TOKEN")
        elif os.getenv("CONFLUENCE_API_TOKEN") and os.getenv("CONFLUENCE_USERNAME"):
            config_params["api_token"] = os.getenv("CONFLUENCE_API_TOKEN")
            config_params["username"] = os.getenv("CONFLUENCE_USERNAME")

        return cls(**config_params)

    @validator('url')
    def validate_url(cls, v):
        """Valide que l'URL est correctement formatée."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError("L'URL doit commencer par http:// ou https://")
        return v


class ProcessingConfig(BaseModel):
    """Configuration pour le traitement des données."""
    chunk_size: int = 1000  # Taille des chunks de texte
    overlap_size: int = 200  # Chevauchement entre les chunks
    max_parallel_downloads: int = 5  # Nombre maximum de téléchargements simultanés
    max_thread_workers: int = 5  # Nombre maximum de workers pour le ThreadPoolExecutor

    @classmethod
    def from_env(cls):
        """Crée une configuration de traitement à partir des variables d'environnement."""
        return cls(
            chunk_size=int(os.getenv("CHUNK_SIZE", "1000")),
            overlap_size=int(os.getenv("OVERLAP_SIZE", "200")),
            max_parallel_downloads=int(os.getenv("MAX_PARALLEL_DOWNLOADS", "5")),
            max_thread_workers=int(os.getenv("MAX_THREAD_WORKERS", "5"))
        )


class StorageConfig(BaseModel):
    """Configuration pour le stockage des données."""
    storage_type: str = "filesystem"  # "filesystem" ou "gcs"
    output_dir: str = "output_data_dir"  # Pour le stockage sur système de fichiers
    gcs_bucket_name: Optional[str] = None  # Pour le stockage sur GCS
    gcs_base_prefix: str = "confluence_rag"  # Préfixe de base pour les objets GCS
    include_attachments: bool = True  # Inclure les pièces jointes dans le stockage
    attachment_extensions_to_convert: List[str] = Field(default_factory=lambda: [".pdf", ".docx", ".txt"])
    max_attachment_size_mb: int = 50  # Taille maximale des pièces jointes en Mo

    @validator('storage_type')
    def validate_storage_type(cls, v):
        """Valide que le type de stockage est supporté."""
        if v.lower() not in ["filesystem", "gcs"]:
            raise ValueError("Le type de stockage doit être 'filesystem' ou 'gcs'")
        return v.lower()

    @validator('gcs_bucket_name')
    def validate_gcs_bucket(cls, v, values):
        """Valide que le bucket GCS est spécifié si le type de stockage est GCS."""
        if values.get('storage_type') == 'gcs' and not v:
            raise ValueError("Le nom du bucket GCS est requis pour le stockage GCS")
        return v

    @classmethod
    def from_env(cls):
        """Crée une configuration de stockage à partir des variables d'environnement."""
        return cls(
            storage_type=os.getenv("STORAGE_TYPE", "filesystem"),
            output_dir=os.getenv("OUTPUT_DIR", "output_data_dir"),
            gcs_bucket_name=os.getenv("GCS_BUCKET_NAME"),
            gcs_base_prefix=os.getenv("GCS_BASE_PREFIX", "confluence_rag"),
            include_attachments=os.getenv("INCLUDE_ATTACHMENTS", "true").lower() == "true",
            attachment_extensions_to_convert=os.getenv("ATTACHMENT_EXTENSIONS_TO_CONVERT", ".pdf,.docx,.txt").split(","),
            max_attachment_size_mb=int(os.getenv("MAX_ATTACHMENT_SIZE_MB", "50"))
        )


class SearchCriteria(BaseModel):
    """Critères de recherche pour les contenus Confluence."""
    spaces: List[str] = Field(default_factory=list)
    labels: List[str] = Field(default_factory=list)
    types: List[str] = Field(default_factory=list, description="Types de contenu (page, blogpost, etc.)")
    date_min: Optional[str] = None
    date_max: Optional[str] = None
    creators: List[str] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    exclude_labels: List[str] = Field(default_factory=list)
    max_results: int = 100
    include_attachments: bool = True
    attachment_types: List[str] = Field(default_factory=list)
    include_children: bool = True
    max_children_depth: int = 3

    @validator('date_min', 'date_max')
    def validate_date(cls, v):
        """Valide que les dates sont au format YYYY-MM-DD."""
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError("La date doit être au format YYYY-MM-DD")
        return v

    @classmethod
    def from_file(cls, file_path: str = "criteres_recherche.json", gcs_bucket: str = None):
        """
        Charge les critères de recherche depuis un fichier JSON.

        Args:
            file_path: Chemin du fichier local ou chemin dans le bucket GCS
            gcs_bucket: Nom du bucket GCS (si le fichier est sur GCS)
        """
        logger = logging.getLogger(__name__)

        # Vérifier si le fichier est sur GCS
        if gcs_bucket:
            return cls._from_gcs_file(gcs_bucket, file_path)

        # Vérifier si le fichier local existe
        if not os.path.exists(file_path):
            logger.warning(f"Le fichier de critères {file_path} n'existe pas. Création d'un exemple...")
            # Créer un exemple de fichier de critères
            example_criteria = cls._get_example_criteria()

            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(example_criteria, f, indent=2, ensure_ascii=False)
                logger.info(f"Exemple de fichier de critères créé: {file_path}")
            except Exception as e:
                logger.error(f"Erreur lors de la création du fichier de critères: {e}")
                return cls()

        # Charger le fichier local
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                criteria_data = json.load(f)
            logger.info(f"Critères de recherche chargés depuis le fichier local: {file_path}")
            return cls(**criteria_data)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des critères de recherche depuis le fichier local: {e}")
            return cls()

    @classmethod
    def _from_gcs_file(cls, bucket_name: str, file_path: str):
        """Charge les critères de recherche depuis un fichier JSON stocké sur GCS."""
        logger = logging.getLogger(__name__)

        if not GCS_AVAILABLE:
            logger.error("Le module google-cloud-storage n'est pas installé. Impossible de charger depuis GCS.")
            return cls()

        try:
            # Initialiser le client GCS
            client = storage.Client()
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(file_path)

            # Vérifier si le blob existe
            if not blob.exists():
                logger.warning(f"Le fichier {file_path} n'existe pas dans le bucket {bucket_name}. Création d'un exemple...")

                # Créer un exemple de fichier de critères
                example_criteria = cls._get_example_criteria()

                # Convertir en JSON et uploader sur GCS
                json_data = json.dumps(example_criteria, indent=2, ensure_ascii=False)
                blob.upload_from_string(json_data, content_type='application/json')

                logger.info(f"Exemple de fichier de critères créé dans gs://{bucket_name}/{file_path}")
                return cls(**example_criteria)

            # Télécharger le contenu du blob
            json_content = blob.download_as_text()
            criteria_data = json.loads(json_content)

            logger.info(f"Critères de recherche chargés depuis gs://{bucket_name}/{file_path}")
            return cls(**criteria_data)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des critères de recherche depuis GCS: {e}")
            return cls()

    @staticmethod
    def _get_example_criteria():
        """Retourne un exemple de critères de recherche."""
        return {
            "spaces": ["EXAMPLE", "DOCS", "TECH"],
            "labels": ["documentation", "technique", "api"],
            "types": ["page", "blogpost"],
            "date_min": "2023-01-01",
            "date_max": "2023-12-31",
            "creators": ["user1", "user2"],
            "keywords": ["architecture", "microservices", "cloud"],
            "exclude_labels": ["obsolete", "archived"],
            "max_results": 100,
            "include_attachments": True,
            "attachment_types": ["pdf", "docx", "xlsx", "pptx"],
            "include_children": True,
            "max_children_depth": 3
        }

    def to_cql(self) -> str:
        """Convertit les critères en requête CQL (Confluence Query Language)."""
        cql_parts = []

        # Espaces
        if self.spaces:
            space_conditions = " OR ".join([f"space = \"{space}\"" for space in self.spaces])
            cql_parts.append(f"({space_conditions})")

        # Labels
        if self.labels:
            label_conditions = " OR ".join([f"labelText ~ \"{label}\"" for label in self.labels])
            cql_parts.append(f"({label_conditions})")

        # Types de contenu
        if self.types:
            type_conditions = " OR ".join([f"type = \"{content_type}\"" for content_type in self.types])
            cql_parts.append(f"({type_conditions})")

        # Dates
        if self.date_min:
            cql_parts.append(f"created >= \"{self.date_min}\"")
        if self.date_max:
            cql_parts.append(f"created <= \"{self.date_max}\"")

        # Créateurs
        if self.creators:
            creator_conditions = " OR ".join([f"creator = \"{creator}\"" for creator in self.creators])
            cql_parts.append(f"({creator_conditions})")

        # Mots-clés (recherche dans le titre et le contenu)
        if self.keywords:
            keyword_conditions = " OR ".join([f"text ~ \"{keyword}\"" for keyword in self.keywords])
            cql_parts.append(f"({keyword_conditions})")

        # Labels à exclure
        if self.exclude_labels:
            exclude_conditions = " AND ".join([f"labelText !~ \"{label}\"" for label in self.exclude_labels])
            cql_parts.append(f"({exclude_conditions})")

        # Assembler la requête CQL
        cql = " AND ".join(cql_parts)
        return cql if cql else "type != null"  # Requête par défaut si aucun critère n'est spécifié